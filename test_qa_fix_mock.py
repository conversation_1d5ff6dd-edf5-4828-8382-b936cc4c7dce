#!/usr/bin/env python3
"""
测试问答系统修复 - 使用模拟embedding
"""

import asyncio
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.quick_qa_base.category_qa_storage import CategoryQAStorage


class MockEmbeddingFunc:
    """模拟embedding函数"""
    def __init__(self):
        self.embedding_dim = 128
    
    async def __call__(self, texts):
        """返回模拟的embedding向量"""
        return [np.random.rand(self.embedding_dim).tolist() for _ in texts]


async def test_category_qa_storage():
    """测试CategoryQAStorage的清空功能"""
    print("开始测试CategoryQAStorage清空功能...")
    
    try:
        # 创建模拟配置
        global_config = {
            "working_dir": "./test_data/Q_A_Base",
            "embedding_batch_num": 10
        }
        
        # 创建存储目录
        os.makedirs(global_config["working_dir"], exist_ok=True)
        
        # 创建CategoryQAStorage实例
        storage = CategoryQAStorage(
            namespace="test",
            workspace="test",
            global_config=global_config,
            embedding_func=MockEmbeddingFunc(),
            similarity_threshold=0.98
        )
        
        # 初始化
        success = await storage.initialize()
        if not success:
            print("❌ 存储初始化失败")
            return False
        
        print("✅ 存储初始化成功")
        
        # 添加一些测试数据
        test_qa_pairs = [
            {"question": "什么是Python?", "answer": "Python是一种编程语言", "category": "programming"},
            {"question": "什么是机器学习?", "answer": "机器学习是人工智能的一个分支", "category": "ai"},
            {"question": "什么是数据库?", "answer": "数据库是存储数据的系统", "category": "database"}
        ]
        
        for qa_pair in test_qa_pairs:
            qa_id = await storage.add_qa_pair(**qa_pair)
            if qa_id:
                print(f"✅ 添加问答对成功: {qa_pair['question']} (ID: {qa_id})")
            else:
                print(f"❌ 添加问答对失败: {qa_pair['question']}")
        
        # 保存数据
        await storage.index_done_callback()
        
        # 检查数据是否存在
        stats = storage.get_statistics()
        print(f"📊 当前数据统计: {stats}")
        
        # 测试清空所有数据
        print("\n测试清空所有数据...")
        result = await storage.clear_all_data()
        if result["status"] == "success":
            print("✅ 清空所有数据成功")
        else:
            print(f"❌ 清空所有数据失败: {result['message']}")
            return False
        
        # 验证数据是否已清空
        stats_after = storage.get_statistics()
        print(f"📊 清空后数据统计: {stats_after}")
        
        if stats_after.get("total_qa_pairs", 0) == 0:
            print("✅ 数据清空验证成功")
        else:
            print("❌ 数据清空验证失败")
            return False
        
        # 重新添加数据测试分类清空
        print("\n测试分类数据清空...")
        for qa_pair in test_qa_pairs:
            await storage.add_qa_pair(**qa_pair)
        await storage.index_done_callback()
        
        # 清空特定分类
        result = await storage.clear_category_data("programming")
        if result["status"] == "success":
            print("✅ 清空分类数据成功")
        else:
            print(f"❌ 清空分类数据失败: {result['message']}")
            return False
        
        # 验证分类数据是否已清空
        categories = storage.get_categories()
        print(f"📊 剩余分类: {categories}")
        
        # 测试删除单个问答对
        print("\n测试删除单个问答对...")
        qa_pairs_list = storage.list_qa_pairs()
        if qa_pairs_list:
            qa_id_to_delete = qa_pairs_list[0]["id"]
            result = await storage.delete_qa_pair(qa_id_to_delete)
            if result["status"] == "success":
                print(f"✅ 删除问答对成功: {qa_id_to_delete}")
            else:
                print(f"❌ 删除问答对失败: {result['message']}")
                return False
        
        print("✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_category_qa_storage())
    if success:
        print("\n🎉 所有测试通过!")
    else:
        print("\n💥 测试失败!")
        sys.exit(1)
