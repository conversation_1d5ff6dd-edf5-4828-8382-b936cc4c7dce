# 问答系统修复和功能增强总结

## 问题描述

原始错误信息：
```json
{
  "success": false,
  "message": "清空问答数据失败：'CategoryQAStorage' object has no attribute '_rebuild_vector_index'",
  "error_code": "HTTP_500",
  "path": "/api/v1/qa/clear"
}
```

## 问题分析

1. **根本原因**: `CategoryQAStorage` 类没有 `_rebuild_vector_index()` 方法，但清空数据的API试图调用这个方法
2. **设计问题**: 清空数据的逻辑不完整，没有考虑到 `CategoryQAStorage` 的特殊结构
3. **功能缺失**: 缺少删除特定问答库数据的功能

## 修复内容

### 1. 修复 CategoryQAStorage 类

**文件**: `core/quick_qa_base/category_qa_storage.py`

新增方法：

#### `clear_all_data()` - 清空所有分类数据
```python
async def clear_all_data(self) -> Dict[str, str]:
    """清空所有分类的问答数据"""
    try:
        # 清空所有分类存储
        for category, storage in self.category_storages.items():
            await storage.drop()
        
        # 清空全局索引
        self.qa_pairs.clear()
        self.category_storages.clear()
        
        logger.info("All QA data cleared successfully")
        return {"status": "success", "message": "所有问答数据已清空"}
    except Exception as e:
        logger.error(f"Error clearing all QA data: {e}")
        return {"status": "error", "message": str(e)}
```

#### `clear_category_data()` - 清空指定分类数据
```python
async def clear_category_data(self, category: str) -> Dict[str, str]:
    """清空指定分类的问答数据"""
    try:
        if category not in self.category_storages:
            return {"status": "error", "message": f"分类 '{category}' 不存在"}
        
        # 清空指定分类的存储
        storage = self.category_storages[category]
        await storage.drop()
        
        # 从全局索引中移除该分类的问答对
        qa_ids_to_remove = [qa_id for qa_id, qa_pair in self.qa_pairs.items() 
                           if qa_pair.category == category]
        for qa_id in qa_ids_to_remove:
            del self.qa_pairs[qa_id]
        
        # 移除分类存储
        del self.category_storages[category]
        
        logger.info(f"Category '{category}' data cleared successfully")
        return {"status": "success", "message": f"分类 '{category}' 的问答数据已清空"}
    except Exception as e:
        logger.error(f"Error clearing category '{category}' data: {e}")
        return {"status": "error", "message": str(e)}
```

#### `delete_qa_pair()` - 删除指定问答对
```python
async def delete_qa_pair(self, qa_id: str) -> Dict[str, str]:
    """删除指定的问答对"""
    try:
        if qa_id not in self.qa_pairs:
            return {"status": "error", "message": f"问答对 '{qa_id}' 不存在"}
        
        qa_pair = self.qa_pairs[qa_id]
        category = qa_pair.category
        
        # 从分类存储中删除
        if category in self.category_storages:
            storage = self.category_storages[category]
            await storage.delete([qa_id])
            await storage.index_done_callback()
        
        # 从全局索引中删除
        del self.qa_pairs[qa_id]
        
        logger.info(f"QA pair '{qa_id}' deleted successfully")
        return {"status": "success", "message": f"问答对 '{qa_id}' 已删除"}
    except Exception as e:
        logger.error(f"Error deleting QA pair '{qa_id}': {e}")
        return {"status": "error", "message": str(e)}
```

### 2. 修复 API 路由

**文件**: `routers/qa_router.py`

#### 修复清空数据API
```python
@router.delete("/clear", response_model=BaseResponse, summary="清空问答数据")
async def clear_qa_data():
    """清空所有问答数据"""
    try:
        handler = await get_qa_api_handler()
        
        # 清空所有问答数据
        if handler.qa_handler and handler.qa_handler.qa_manager:
            storage = handler.qa_handler.qa_manager.storage
            
            # 检查存储类型并调用相应的清空方法
            if hasattr(storage, 'clear_all_data'):
                # CategoryQAStorage
                result = await storage.clear_all_data()
                if result["status"] != "success":
                    raise Exception(result["message"])
            elif hasattr(storage, 'drop'):
                # QAVectorStorage
                result = await storage.drop()
                if result["status"] != "success":
                    raise Exception(result["message"])
            else:
                # 兜底方案：清空qa_pairs并保存
                storage.qa_pairs.clear()
                await storage.index_done_callback()
        
        return BaseResponse(success=True, message="问答数据已清空")
        
    except Exception as e:
        logger.error(f"Clear QA data error: {e}")
        raise HTTPException(status_code=500, detail=f"清空问答数据失败: {str(e)}")
```

#### 新增清空分类数据API
```python
@router.delete("/category/{category}", response_model=BaseResponse, summary="清空指定分类的问答数据")
async def clear_category_data(category: str):
    """清空指定分类的问答数据"""
    # 实现逻辑...
```

#### 新增删除单个问答对API
```python
@router.delete("/qa/{qa_id}", response_model=BaseResponse, summary="删除指定的问答对")
async def delete_qa_pair(qa_id: str):
    """删除指定的问答对"""
    # 实现逻辑...
```

## 新增功能

### 1. 清空指定分类数据
- **端点**: `DELETE /api/v1/qa/category/{category}`
- **功能**: 清空指定分类的所有问答数据
- **参数**: `category` - 分类名称

### 2. 删除单个问答对
- **端点**: `DELETE /api/v1/qa/qa/{qa_id}`
- **功能**: 删除指定ID的问答对
- **参数**: `qa_id` - 问答对ID

## 兼容性保证

修复后的代码同时支持：
1. **CategoryQAStorage** - 分类存储（当前使用）
2. **QAVectorStorage** - 单一存储（向后兼容）

通过检查存储对象的方法来决定使用哪种清空策略，确保不同存储类型都能正常工作。

## 测试验证

已通过以下测试验证修复效果：
1. ✅ 清空所有数据功能
2. ✅ 清空指定分类数据功能  
3. ✅ 删除单个问答对功能
4. ✅ 数据完整性验证
5. ✅ 错误处理验证

## 使用示例

### 清空所有问答数据
```bash
curl -X DELETE "http://localhost:8000/api/v1/qa/clear"
```

### 清空指定分类数据
```bash
curl -X DELETE "http://localhost:8000/api/v1/qa/category/programming"
```

### 删除单个问答对
```bash
curl -X DELETE "http://localhost:8000/api/v1/qa/qa/qa_12345678"
```

## 总结

本次修复解决了原始的 `_rebuild_vector_index` 方法缺失问题，并新增了删除特定问答库数据的功能。修复后的系统具有更好的数据管理能力和更完善的API接口。
